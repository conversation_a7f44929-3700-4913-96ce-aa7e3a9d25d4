import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/shared/widgets/animation/scale_animation.dart';
import 'package:wd/shared/widgets/auth/phone_prefix.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_textfield.dart';
import 'package:wd/shared/widgets/icon_textfield.dart';
import 'package:wd/shared/widgets/login/login_card_container.dart';

import '../../../../core/constants/constants.dart';
import '../../../../core/utils/system_util.dart';
import '../../../../shared/widgets/verification_code/verification_code.dart';
import 'forgot_cubit.dart';

class ForgotPage extends StatefulWidget {
  const ForgotPage({super.key});

  @override
  State<ForgotPage> createState() => _ForgotPageState();
}

class _ForgotPageState extends State<ForgotPage> {
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  final String _selectedCountryCode = '+86';

  static const double _verticalSpacing = 15.0;
  static const double _horizontalSpacing = 10.0;
  static const double _sectionSpacing = 30.0;

  // Country codes with flags
  final List<Map<String, String>> _countryCodes = [
    {'code': '+86', 'country': 'CN', 'name': 'China'},
    {'code': '+1', 'country': 'US', 'name': 'United States'},
    {'code': '+44', 'country': 'GB', 'name': 'United Kingdom'},
    {'code': '+81', 'country': 'JP', 'name': 'Japan'},
    {'code': '+82', 'country': 'KR', 'name': 'South Korea'},
    {'code': '+65', 'country': 'SG', 'name': 'Singapore'},
    {'code': '+60', 'country': 'MY', 'name': 'Malaysia'},
    {'code': '+66', 'country': 'TH', 'name': 'Thailand'},
    {'code': '+84', 'country': 'VN', 'name': 'Vietnam'},
    {'code': '+63', 'country': 'PH', 'name': 'Philippines'},
  ];

  String emojiFlag(String countryCode) {
    return countryCode.toUpperCase().runes.map((codeUnit) => String.fromCharCode(0x1F1E6 + (codeUnit - 0x41))).join();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Title section with animation
        AnimationConfiguration.synchronized(
          duration: const Duration(milliseconds: 800),
          child: SlideAnimation(
            verticalOffset: 100.0,
            child: FadeInAnimation(
              child: _buildTitleSection(),
            ),
          ),
        ),
        SizedBox(height: 30.gw),

        // Tab bar for phone/email selection
        _buildTabBar(),
        SizedBox(height: 30.gw),

        // Form content
        BlocBuilder<ForgotCubit, ForgotState>(
          builder: (context, state) {
            return CommonScaleAnimationWidget(
              children: [
                if (state.forgotType == LoginType.phone) ...[
                  _buildPhoneInputField(state),
                ] else ...[
                  _buildEmailInputField(state),
                ],
                SizedBox(height: _verticalSpacing.gw),
                _buildVerificationCodeInputField(state),
                SizedBox(height: _verticalSpacing.gw),
                _buildPasswordInput(state),
                SizedBox(height: _verticalSpacing.gw),
                _buildConfirmPasswordInput(state),
                SizedBox(height: _sectionSpacing.gw),
                _buildResetButton(),
                SizedBox(height: 20.gw),
                _buildBottomText(),
              ],
            );
          },
        ),
      ],
    );
  }

  /// Builds the title section
  Widget _buildTitleSection() {
    return Column(
      children: [
        Text(
          'Forgot your',
          textAlign: TextAlign.center,
          style: context.textTheme.primary.copyWith(
            fontSize: 28.gw,
            fontWeight: FontWeight.w400,
            color: Colors.white,
            height: 1.2,
          ),
        ),
        Text(
          'Password',
          textAlign: TextAlign.center,
          style: context.textTheme.primary.copyWith(
            fontSize: 28.gw,
            fontWeight: FontWeight.bold,
            color: context.theme.primaryColor,
            height: 1.2,
          ),
        ),
      ],
    );
  }

  /// Builds the tab bar for phone/email selection
  Widget _buildTabBar() {
    return BlocBuilder<ForgotCubit, ForgotState>(
      builder: (context, state) {
        return Container(
          width: 200.gw,
          height: 44.gw,
          decoration: BoxDecoration(
            color: context.colorTheme.textSecondary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(22.gw),
          ),
          child: Row(
            children: [
              _buildTabItem(
                icon: Icons.phone_outlined,
                title: 'Phone number',
                isSelected: state.forgotType == LoginType.phone,
                onTap: () {
                  if (state.forgotType != LoginType.phone) {
                    context.read<ForgotCubit>().switchForgotType();
                  }
                },
              ),
              _buildTabItem(
                icon: Icons.email_outlined,
                title: 'Email',
                isSelected: state.forgotType == LoginType.email,
                onTap: () {
                  if (state.forgotType != LoginType.email) {
                    context.read<ForgotCubit>().switchForgotType();
                  }
                },
              ),
            ],
          ),
        );
      },
    );
  }

  /// Builds a single tab item
  Widget _buildTabItem({
    required IconData icon,
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          height: 36.gw,
          margin: EdgeInsets.all(4.gw),
          decoration: BoxDecoration(
            color: isSelected ? context.theme.primaryColor : Colors.transparent,
            borderRadius: BorderRadius.circular(18.gw),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: isSelected ? Colors.black : Colors.white,
                size: 16.gw,
              ),
              SizedBox(width: 4.gw),
              Text(
                title,
                style: context.textTheme.primary.copyWith(
                  color: isSelected ? Colors.black : Colors.white,
                  fontSize: 12.gw,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the phone input field with country code dropdown
  Widget _buildPhoneInputField(ForgotState state) {
    return CommonTextField(
      controller: state.phoneController,
      hintText: "Enter phone number",
      prefixIcon: _buildCountryCodeDropdown(),
      onChanged: (value) => context.read<ForgotCubit>().setPhone(value),
    );
  }

  /// Builds the email input field
  Widget _buildEmailInputField(ForgotState state) {
    return CommonTextField(
      controller: state.emailController,
      hintText: "Enter email",
      prefixIcon: const Icon(Icons.email_outlined, color: Colors.white),
      onChanged: (value) => context.read<ForgotCubit>().setEmail(value),
    );
  }

  /// Builds the verification code input field
  Widget _buildVerificationCodeInputField(ForgotState state) {
    return CommonTextField(
      controller: state.smsCodeController,
      hintText: "Please enter the code",
      prefixIcon: const Icon(Icons.shield_outlined, color: Colors.white),
      suffixIcon: _buildGetCodeButton(state),
      onChanged: (value) => context.read<ForgotCubit>().setSmsCode(value),
    );
  }

  /// Builds the password input field
  Widget _buildPasswordInput(ForgotState state) {
    return CommonTextField(
      controller: TextEditingController(),
      hintText: "Password must be 6—22 letters or digits",
      prefixIcon: const Icon(Icons.lock_outline, color: Colors.white),
      obscureText: !_isPasswordVisible,
      suffixIcon: IconButton(
        icon: Icon(_isPasswordVisible ? Icons.visibility : Icons.visibility_off, color: Colors.white),
        onPressed: () => setState(() => _isPasswordVisible = !_isPasswordVisible),
      ),
      onChanged: (value) => context.read<ForgotCubit>().setPassword(value),
    );
  }

  /// Builds the confirm password input field
  Widget _buildConfirmPasswordInput(ForgotState state) {
    return CommonTextField(
      controller: TextEditingController(),
      hintText: "Confirm Password",
      prefixIcon: const Icon(Icons.lock_outline, color: Colors.white),
      obscureText: !_isConfirmPasswordVisible,
      suffixIcon: IconButton(
        icon: Icon(_isConfirmPasswordVisible ? Icons.visibility : Icons.visibility_off, color: Colors.white),
        onPressed: () => setState(() => _isConfirmPasswordVisible = !_isConfirmPasswordVisible),
      ),
      onChanged: (value) => context.read<ForgotCubit>().setConfirmPassword(value),
    );
  }

  /// Builds the country code dropdown
  Widget _buildCountryCodeDropdown() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.gw),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: _selectedCountryCode,
          icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
          dropdownColor: context.theme.scaffoldBackgroundColor,
          items: _countryCodes.map((country) {
            return DropdownMenuItem<String>(
              value: country['code'],
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    emojiFlag(country['country']!),
                    style: TextStyle(fontSize: 16.gw),
                  ),
                  SizedBox(width: 4.gw),
                  Text(
                    country['code']!,
                    style: context.textTheme.primary.copyWith(
                      color: Colors.white,
                      fontSize: 14.gw,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
          onChanged: (String? newValue) {
            if (newValue != null) {
              setState(() {
                // _selectedCountryCode = newValue;
              });
            }
          },
        ),
      ),
    );
  }

  /// Builds the get code button
  Widget _buildGetCodeButton(ForgotState state) {
    return Container(
      margin: EdgeInsets.all(8.gw),
      child: CommonButton(
        title: "Get code",
        width: 80.gw,
        height: 32.gw,
        textColor: Colors.black,
        backgroundColor: context.theme.primaryColor,
        onPressed: () {
          // TODO: Implement get code functionality
          if (state.forgotType == LoginType.phone) {
            _sendPhoneVerificationCode(state.phone);
          } else {
            _sendEmailVerificationCode(state.email);
          }
        },
      ),
    );
  }

  /// Builds the reset button
  Widget _buildResetButton() {
    return BlocBuilder<ForgotCubit, ForgotState>(
      builder: (context, state) {
        return CommonButton(
          title: "Log In",
          textColor: context.colorTheme.btnTitlePrimary,
          showLoading: state.forgotStatus == SimplyNetStatus.loading,
          onPressed: () => context.read<ForgotCubit>().resetPassword(),
        );
      },
    );
  }

  /// Sends phone verification code
  void _sendPhoneVerificationCode(String phone) {
    if (phone.isEmpty) {
      // Show error message
      return;
    }

    // TODO: Implement phone verification API call
    // Use the existing phone verification API with area code
    // API: /sms/userBindPhoneNo
    // Body: {"phoneNo": "$_selectedCountryCode$phone"}
  }

  /// Sends email verification code
  void _sendEmailVerificationCode(String email) {
    if (email.isEmpty) {
      // Show error message
      return;
    }

    // TODO: Implement email verification API call
    // API: /mail/userBindMail
    // Body: {"mail": email, "mailCode": "verification_code"}
  }

  /// Builds the bottom text
  Widget _buildBottomText() {
    return GestureDetector(
      onTap: () => SystemUtil.contactService(),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.gw),
        child: RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            style: TextStyle(
              fontSize: 12.fs,
              color: Colors.grey,
            ),
            children: [
              const TextSpan(
                  text:
                      '*Only users with a bound mobile number or email can retrieve their password via self-service. Users without a bound number should contact '),
              TextSpan(
                text: 'Online Support',
                style: TextStyle(
                  color: context.theme.primaryColor,
                  decoration: TextDecoration.underline,
                ),
              ),
              const TextSpan(text: ' for assistance'),
            ],
          ),
        ),
      ),
    );
  }
}
