import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/shared/widgets/animation/scale_animation.dart';
import 'package:wd/shared/widgets/auth/auth_header.dart';
import 'package:wd/shared/widgets/auth/auth_tab_bar.dart';
import 'package:wd/shared/widgets/text_fields/phone_input_field.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_textfield.dart';
import 'package:wd/core/models/country.dart';
import 'package:wd/core/services/country_service.dart';

import '../../../../core/constants/constants.dart';
import '../../../../core/utils/system_util.dart';
import '../forgot/forgot_cubit.dart';

class SetNewPasswordPage extends StatefulWidget {
  final LoginType resetType;
  final String contactInfo; // phone or email

  const SetNewPasswordPage({
    super.key,
    required this.resetType,
    required this.contactInfo,
  });

  @override
  State<SetNewPasswordPage> createState() => _SetNewPasswordPageState();
}

class _SetNewPasswordPageState extends State<SetNewPasswordPage> {
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  Country? _selectedCountry;

  static const double _verticalSpacing = 15.0;
  static const double _horizontalSpacing = 10.0;
  static const double _sectionSpacing = 30.0;

  @override
  void initState() {
    super.initState();
    _initializeCountry();
  }

  Future<void> _initializeCountry() async {
    final defaultCountry = await CountryService.instance.getDefaultCountry();
    if (mounted) {
      setState(() {
        _selectedCountry = defaultCountry;
      });
    }
  }

  String emojiFlag(String countryCode) {
    return countryCode.toUpperCase().runes.map((codeUnit) => String.fromCharCode(0x1F1E6 + (codeUnit - 0x41))).join();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Title section with animation
        AnimationConfiguration.synchronized(
          duration: const Duration(milliseconds: 800),
          child: SlideAnimation(
            verticalOffset: 100.0,
            child: FadeInAnimation(
              child: _buildTitleSection(),
            ),
          ),
        ),
        SizedBox(height: 30.gw),

        // Tab bar for phone/email selection
        PhoneEmailTabBar(
          selectedIndex: widget.resetType == LoginType.phone ? 0 : 1,
          onTabChanged: (index) {
            // Tab switching is handled by parent widget
          },
        ),
        SizedBox(height: 30.gw),

        // Form content
        BlocBuilder<ForgotCubit, ForgotState>(
          builder: (context, state) {
            return CommonScaleAnimationWidget(
              children: [
                if (widget.resetType == LoginType.phone) ...[
                  _buildPhoneInputField(),
                ] else ...[
                  _buildEmailInputField(),
                ],
                SizedBox(height: _verticalSpacing.gw),
                _buildVerificationCodeInputField(),
                SizedBox(height: _verticalSpacing.gw),
                _buildPasswordInput(),
                SizedBox(height: _verticalSpacing.gw),
                _buildConfirmPasswordInput(),
                SizedBox(height: _sectionSpacing.gw),
                _buildSetPasswordButton(),
                SizedBox(height: 20.gw),
                _buildBottomText(),
              ],
            );
          },
        ),
      ],
    );
  }

  /// Builds the title section
  Widget _buildTitleSection() {
    return Column(
      children: [
        Text(
          'Set New',
          textAlign: TextAlign.center,
          style: context.textTheme.primary.copyWith(
            fontSize: 28.gw,
            fontWeight: FontWeight.w400,
            color: Colors.white,
            height: 1.2,
          ),
        ),
        Text(
          'Password',
          textAlign: TextAlign.center,
          style: context.textTheme.primary.copyWith(
            fontSize: 28.gw,
            fontWeight: FontWeight.bold,
            color: context.theme.primaryColor,
            height: 1.2,
          ),
        ),
      ],
    );
  }

  /// Builds the tab bar for phone/email selection
  Widget _buildTabBar() {
    return Container(
      width: 200.gw,
      height: 44.gw,
      decoration: BoxDecoration(
        color: context.colorTheme.textSecondary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(22.gw),
      ),
      child: Row(
        children: [
          _buildTabItem(
            icon: Icons.phone_outlined,
            title: 'Phone number',
            isSelected: widget.resetType == LoginType.phone,
            onTap: () {
              // Tab switching would require parent widget to handle
            },
          ),
          _buildTabItem(
            icon: Icons.email_outlined,
            title: 'Email',
            isSelected: widget.resetType == LoginType.email,
            onTap: () {
              // Tab switching would require parent widget to handle
            },
          ),
        ],
      ),
    );
  }

  /// Builds a single tab item
  Widget _buildTabItem({
    required IconData icon,
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          height: 36.gw,
          margin: EdgeInsets.all(4.gw),
          decoration: BoxDecoration(
            color: isSelected ? context.theme.primaryColor : Colors.transparent,
            borderRadius: BorderRadius.circular(18.gw),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: isSelected ? Colors.black : Colors.white,
                size: 16.gw,
              ),
              SizedBox(width: 4.gw),
              Text(
                title,
                style: context.textTheme.primary.copyWith(
                  color: isSelected ? Colors.black : Colors.white,
                  fontSize: 12.gw,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the phone input field with country code dropdown
  Widget _buildPhoneInputField() {
    return CommonTextField(
      controller: TextEditingController()..text = widget.contactInfo,
      hintText: "Enter phone number",
      prefixIcon: _buildCountryCodeDropdown(),
      inputEnable: false, // Read-only since it's already verified
    );
  }

  /// Builds the email input field
  Widget _buildEmailInputField() {
    return CommonTextField(
      controller: TextEditingController()..text = widget.contactInfo,
      hintText: "Enter email",
      prefixIcon: const Icon(Icons.email_outlined, color: Colors.white),
      inputEnable: false, // Read-only since it's already verified
    );
  }

  /// Builds the verification code input field
  Widget _buildVerificationCodeInputField() {
    return CommonTextField(
      controller: TextEditingController(),
      hintText: "Please enter the code",
      prefixIcon: const Icon(Icons.shield_outlined, color: Colors.white),
      suffixIcon: _buildGetCodeButton(),
    );
  }

  /// Builds the password input field
  Widget _buildPasswordInput() {
    return CommonTextField(
      controller: TextEditingController(),
      hintText: "Password must be 6—22 letters or digits",
      prefixIcon: const Icon(Icons.lock_outline, color: Colors.white),
      obscureText: !_isPasswordVisible,
      suffixIcon: IconButton(
        icon: Icon(_isPasswordVisible ? Icons.visibility : Icons.visibility_off, color: Colors.white),
        onPressed: () => setState(() => _isPasswordVisible = !_isPasswordVisible),
      ),
    );
  }

  /// Builds the confirm password input field
  Widget _buildConfirmPasswordInput() {
    return CommonTextField(
      controller: TextEditingController(),
      hintText: "Confirm Password",
      prefixIcon: const Icon(Icons.lock_outline, color: Colors.white),
      obscureText: !_isConfirmPasswordVisible,
      suffixIcon: IconButton(
        icon: Icon(_isConfirmPasswordVisible ? Icons.visibility : Icons.visibility_off, color: Colors.white),
        onPressed: () => setState(() => _isConfirmPasswordVisible = !_isConfirmPasswordVisible),
      ),
    );
  }

  /// Builds the country code dropdown
  Widget _buildCountryCodeDropdown() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.gw),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: _selectedCountryCode,
          icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
          dropdownColor: context.theme.scaffoldBackgroundColor,
          items: _countryCodes.map((country) {
            return DropdownMenuItem<String>(
              value: country['code'],
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    emojiFlag(country['country']!),
                    style: TextStyle(fontSize: 16.gw),
                  ),
                  SizedBox(width: 4.gw),
                  Text(
                    country['code']!,
                    style: context.textTheme.primary.copyWith(
                      color: Colors.white,
                      fontSize: 14.gw,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
          onChanged: (String? newValue) {
            if (newValue != null) {
              setState(() {
                _selectedCountryCode = newValue;
              });
            }
          },
        ),
      ),
    );
  }

  /// Builds the get code button
  Widget _buildGetCodeButton() {
    return Container(
      margin: EdgeInsets.all(8.gw),
      child: CommonButton(
        title: "Get code",
        width: 80.gw,
        height: 32.gw,
        textColor: Colors.black,
        backgroundColor: context.theme.primaryColor,
        onPressed: () {
          // TODO: Implement get code functionality for set new password
        },
      ),
    );
  }

  /// Builds the set password button
  Widget _buildSetPasswordButton() {
    return CommonButton(
      title: "Log In",
      textColor: context.colorTheme.btnTitlePrimary,
      onPressed: () {
        // TODO: Implement set new password functionality
      },
    );
  }

  /// Builds the bottom text
  Widget _buildBottomText() {
    return GestureDetector(
      onTap: () => SystemUtil.contactService(),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.gw),
        child: RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            style: TextStyle(
              fontSize: 12.fs,
              color: Colors.grey,
            ),
            children: [
              const TextSpan(
                  text:
                      '*Only users with a bound mobile number or email can retrieve their password via self-service. Users without a bound number should contact '),
              TextSpan(
                text: 'Online Support',
                style: TextStyle(
                  color: context.theme.primaryColor,
                  decoration: TextDecoration.underline,
                ),
              ),
              const TextSpan(text: ' for assistance'),
            ],
          ),
        ),
      ),
    );
  }
}
