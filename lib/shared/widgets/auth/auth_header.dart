import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/screen_util.dart';
import 'package:wd/shared/app/extension/color_extension.dart';
import 'package:wd/shared/constants/assets.dart';
import 'package:wd/shared/logic/sys_settings/sys_settings_cubit.dart';
import 'package:wd/shared/models/sys_settings_model/sys_settings_model.dart';
import 'package:wd/core/utils/utils.dart';
import 'package:wd/core/utils/icon_helper.dart';
import 'package:wd/features/home/<USER>/settings_menu.dart';

import '../../../core/constants/assets.dart';

class AuthHeader extends StatelessWidget {
  final String? title;
  final String? subtitle;
  final double? height;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final bool showLanguageButton;

  const AuthHeader({
    super.key,
    this.title,
    this.subtitle,
    this.height,
    this.showBackButton = true,
    this.onBackPressed,
    this.showLanguageButton = true,
  });

  @override
  Widget build(BuildContext context) {
    final headerHeight = height ?? (MediaQuery.of(context).size.height * 0.36);
    
    return BlocSelector<SysSettingsCubit, SysSettingsState, SysSettingsModel?>(
      selector: (state) => state.maybeWhen(
        loaded: (sysSettings) => sysSettings,
        orElse: () => null,
      ),
      builder: (context, sysSettings) {
        final darkMode = isDarkMode(context);
        final logoUrl = (darkMode ? sysSettings?.logoDark : sysSettings?.logoLight) ?? '';
        
        return SizedBox(
          width: double.infinity,
          height: headerHeight,
          child: Stack(
            children: [
              // Background image
              Image.asset(
                Assets.loginHeader,
                width: double.infinity,
                height: headerHeight,
                fit: BoxFit.cover,
              ),

              // Top navigation bar
              Positioned(
                top: 50.gh,
                left: 0,
                right: 0,
                child: Stack(
                  children: [
                    // Back button
                    if (showBackButton) ...[
                      Positioned(
                        left: 0,
                        child: BackButton(
                          onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
                        ),
                      ),
                    ],
                    
                    // Centered Logo
                    Center(
                      child: CachedNetworkImage(
                        imageUrl: logoUrl,
                        fit: BoxFit.contain,
                        height: 32.gh,
                        errorWidget: (context, url, error) => SizedBox(height: 32, width: 32),
                      ),
                    ),

                    // Language button
                    if (showLanguageButton) ...[
                      Positioned(
                        right: 14.gw,
                        child: IconButton(
                          onPressed: () => _showLanguageMenu(context),
                          icon: IconHelper.loadAsset(
                            Assets.webIcon,
                            width: 24.gw,
                            height: 24.gh,
                            color: context.colorTheme.textPrimary,
                          ),
                          iconSize: 24.gw,
                          padding: EdgeInsets.all(8.gw),
                        ),
                      ),
                    ],
                  ],
                ),
              ),

              // Header image (if exists)
              Positioned(
                top: 100.gh,
                left: 0,
                right: 0,
                child: Center(
                  child: SizedBox(
                    width: 0.65.gsw,
                    child: Image.asset(
                      Assets.loginHeader2,
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
              ),

              // Title and subtitle section
              if (title != null || subtitle != null) ...[
                Positioned(
                  bottom: 40.gh,
                  left: 0,
                  right: 0,
                  child: Column(
                    children: [
                      if (title != null) ...[
                        Text(
                          title!,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 28.gw,
                            fontWeight: FontWeight.w400,
                            color: Colors.white,
                            height: 1.2,
                          ),
                        ),
                      ],
                      if (subtitle != null) ...[
                        SizedBox(height: 4.gh),
                        Text(
                          subtitle!,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 28.gw,
                            fontWeight: FontWeight.bold,
                            color: context.theme.primaryColor,
                            height: 1.2,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Future<void> _showLanguageMenu(BuildContext context) async {
    final RenderBox button = context.findRenderObject() as RenderBox;
    final buttonPosition = button.localToGlobal(Offset.zero);

    await showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        buttonPosition.dx,
        buttonPosition.dy + 80.gh,
        buttonPosition.dx - 1.gh,
        0,
      ),
      elevation: 0,
      color: Colors.transparent,
      constraints: BoxConstraints(maxWidth: 120.gw),
      items: [
        PopupMenuItem(
          enabled: false,
          padding: EdgeInsets.zero,
          child: Container(
            width: 160.gw,
            decoration: BoxDecoration(
              color: context.theme.cardColor,
              borderRadius: BorderRadius.circular(12.gw),
              boxShadow: [
                BoxShadow(
                  color: context.theme.shadowColor,
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: const LanguageOptions(),
          ),
        ),
      ],
    );
  }
}
