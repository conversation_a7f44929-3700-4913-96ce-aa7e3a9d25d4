import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/screen_util.dart';
import 'package:wd/shared/app/extension/color_extension.dart';

class AuthTabBar extends StatelessWidget {
  final List<String> tabs;
  final int selectedIndex;
  final ValueChanged<int> onTabChanged;
  final List<IconData>? icons;
  final double? width;
  final double? height;
  final bool useAssetImages;
  final List<String>? assetImages;

  const AuthTabBar({
    super.key,
    required this.tabs,
    required this.selectedIndex,
    required this.onTabChanged,
    this.icons,
    this.width,
    this.height,
    this.useAssetImages = false,
    this.assetImages,
  }) : assert(tabs.length >= 2, 'At least 2 tabs are required'),
       assert(icons == null || icons.length == tabs.length, 'Icons length must match tabs length'),
       assert(!useAssetImages || (assetImages != null && assetImages.length == tabs.length), 'Asset images must be provided when useAssetImages is true');

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: width ?? 200.gw,
        height: height ?? 44.gw,
        decoration: BoxDecoration(
          color: context.colorTheme.foregroundColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(22.gw),
        ),
        padding: EdgeInsets.all(4.gw),
        child: Row(
          children: tabs.asMap().entries.map((entry) {
            final index = entry.key;
            final title = entry.value;
            final isSelected = index == selectedIndex;
            
            return Expanded(
              child: _buildTabItem(
                context: context,
                title: title,
                isSelected: isSelected,
                onTap: () => onTabChanged(index),
                icon: icons?[index],
                assetImage: useAssetImages ? assetImages?[index] : null,
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildTabItem({
    required BuildContext context,
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
    IconData? icon,
    String? assetImage,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 36.gw,
        margin: EdgeInsets.all(2.gw),
        decoration: BoxDecoration(
          color: isSelected ? context.theme.primaryColor : Colors.transparent,
          borderRadius: BorderRadius.circular(18.gw),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon or asset image
            if (icon != null) ...[
              Icon(
                icon,
                color: isSelected 
                    ? context.colorTheme.btnTitlePrimary 
                    : context.colorTheme.textPrimary,
                size: 16.gw,
              ),
              SizedBox(width: 4.gw),
            ] else if (assetImage != null) ...[
              Image.asset(
                assetImage,
                width: 16.gw,
                height: 16.gw,
                color: isSelected 
                    ? context.colorTheme.btnTitlePrimary 
                    : context.colorTheme.textPrimary,
              ),
              SizedBox(width: 4.gw),
            ],
            
            // Title
            Flexible(
              child: Text(
                title.tr(),
                style: TextStyle(
                  color: isSelected 
                      ? context.colorTheme.btnTitlePrimary 
                      : context.colorTheme.textPrimary,
                  fontSize: 12.gw,
                  fontWeight: FontWeight.w500,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Predefined auth tab bars for common use cases
class PhoneEmailTabBar extends StatelessWidget {
  final int selectedIndex;
  final ValueChanged<int> onTabChanged;
  final double? width;
  final double? height;

  const PhoneEmailTabBar({
    super.key,
    required this.selectedIndex,
    required this.onTabChanged,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return AuthTabBar(
      tabs: ['phone_login', 'email_login'],
      selectedIndex: selectedIndex,
      onTabChanged: onTabChanged,
      icons: const [Icons.phone_outlined, Icons.email_outlined],
      width: width,
      height: height,
    );
  }
}

class LoginRegisterTabBar extends StatelessWidget {
  final int selectedIndex;
  final ValueChanged<int> onTabChanged;
  final double? width;
  final double? height;
  final bool useAssetImages;
  final List<String>? assetImages;

  const LoginRegisterTabBar({
    super.key,
    required this.selectedIndex,
    required this.onTabChanged,
    this.width,
    this.height,
    this.useAssetImages = false,
    this.assetImages,
  });

  @override
  Widget build(BuildContext context) {
    return AuthTabBar(
      tabs: ['sign_in', 'sign_up'],
      selectedIndex: selectedIndex,
      onTabChanged: onTabChanged,
      width: width,
      height: height,
      useAssetImages: useAssetImages,
      assetImages: assetImages,
    );
  }
}

class PhoneEmailRegisterTabBar extends StatelessWidget {
  final int selectedIndex;
  final ValueChanged<int> onTabChanged;
  final double? width;
  final double? height;

  const PhoneEmailRegisterTabBar({
    super.key,
    required this.selectedIndex,
    required this.onTabChanged,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return AuthTabBar(
      tabs: ['phone_register', 'email_register'],
      selectedIndex: selectedIndex,
      onTabChanged: onTabChanged,
      icons: const [Icons.phone_outlined, Icons.email_outlined],
      width: width,
      height: height,
    );
  }
}
