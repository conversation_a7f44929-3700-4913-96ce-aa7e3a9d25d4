import 'package:flutter/material.dart';
import 'package:wd/core/models/country.dart';
import 'package:wd/core/services/country_service.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';

class CountryDropdown extends StatefulWidget {
  final Country? selectedCountry;
  final ValueChanged<Country> onCountryChanged;
  final bool showFullName;
  final double? width;
  final double? height;

  const CountryDropdown({
    super.key,
    this.selectedCountry,
    required this.onCountryChanged,
    this.showFullName = false,
    this.width,
    this.height,
  });

  @override
  State<CountryDropdown> createState() => _CountryDropdownState();
}

class _CountryDropdownState extends State<CountryDropdown> {
  List<Country> _countries = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCountries();
  }

  Future<void> _loadCountries() async {
    try {
      final countries = await CountryService.instance.getCountries();
      if (mounted) {
        setState(() {
          _countries = countries;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Container(
        width: widget.width ?? 120.gw,
        height: widget.height ?? 48.gw,
        decoration: BoxDecoration(
          border: Border.all(color: context.colorTheme.outline),
          borderRadius: BorderRadius.circular(8.gw),
        ),
        child: const Center(
          child: SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
        ),
      );
    }

    return Container(
      width: widget.width ?? 120.gw,
      height: widget.height ?? 48.gw,
      decoration: BoxDecoration(
        border: Border.all(color: context.colorTheme.outline),
        borderRadius: BorderRadius.circular(8.gw),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<Country>(
          value: widget.selectedCountry,
          isExpanded: true,
          icon: Icon(
            Icons.keyboard_arrow_down,
            color: context.colorTheme.onSurface,
            size: 20.gw,
          ),
          style: TextStyle(
            color: context.colorTheme.onSurface,
            fontSize: 14.gw,
          ),
          dropdownColor: context.colorTheme.surface,
          borderRadius: BorderRadius.circular(8.gw),
          menuMaxHeight: 300.gw,
          items: _countries.map((Country country) {
            return DropdownMenuItem<Country>(
              value: country,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 12.gw),
                child: Row(
                  children: [
                    Text(
                      country.flag,
                      style: TextStyle(fontSize: 16.gw),
                    ),
                    SizedBox(width: 8.gw),
                    if (widget.showFullName) ...[
                      Expanded(
                        child: Text(
                          country.name,
                          style: TextStyle(
                            fontSize: 14.gw,
                            color: context.colorTheme.onSurface,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      SizedBox(width: 4.gw),
                    ],
                    Text(
                      '+${country.areaCode}',
                      style: TextStyle(
                        fontSize: 14.gw,
                        color: context.colorTheme.onSurface,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
          onChanged: (Country? newValue) {
            if (newValue != null) {
              widget.onCountryChanged(newValue);
            }
          },
          selectedItemBuilder: (BuildContext context) {
            return _countries.map((Country country) {
              return Padding(
                padding: EdgeInsets.symmetric(horizontal: 12.gw),
                child: Row(
                  children: [
                    Text(
                      country.flag,
                      style: TextStyle(fontSize: 16.gw),
                    ),
                    SizedBox(width: 8.gw),
                    Text(
                      '+${country.areaCode}',
                      style: TextStyle(
                        fontSize: 14.gw,
                        color: context.colorTheme.onSurface,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              );
            }).toList();
          },
        ),
      ),
    );
  }
}
